# Database
DATABASE_URL="postgresql://username:password@localhost:5432/axie_studio"

# Stripe Configuration
STRIPE_API_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
STRIPE_CUSTOMER_PORTAL_URL="https://billing.stripe.com/p/login/test_..."

# Stripe Price IDs (get these from your Stripe dashboard)
STRIPE_STARTER_PRICE_ID="price_..."
STRIPE_PRO_PRICE_ID="price_..."
STRIPE_ENTERPRISE_PRICE_ID="price_..."

# Axie Studio / Langflow Configuration
LANGFLOW_BASE_URL="https://langflow-tv34o.ondigitalocean.app"
LANGFLOW_API_KEY="entrance"

# JWT Secret (generate a secure random string)
JWT_SECRET="your-super-secret-jwt-key-here"

# Email Configuration (SendGrid)
SENDGRID_API_KEY="SG...."
SENDGRID_FROM_EMAIL="<EMAIL>"

# App URLs
CLIENT_URL="http://localhost:3000"
SERVER_URL="http://localhost:3001"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
