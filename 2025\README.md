# Axie Studio - AI Workflow Platform

🚀 **Build AI Workflows That Actually Work**

Axie Studio is a complete SaaS platform that empowers creators and businesses to build, deploy, and scale AI workflows without writing code. Built with Wasp, React, and integrated with your existing Langflow backend.

## ✨ Features

- 🎨 **Visual Workflow Builder** - Drag & drop AI components
- 💳 **Stripe Integration** - Complete payment processing with multiple plans
- 🔐 **User Authentication** - Email/password + social auth
- 📊 **Usage Analytics** - Track executions, credits, and performance
- 🔗 **300+ Integrations** - Connect to any service
- ⚡ **Real-time Execution** - Sub-second response times
- 🛡️ **Enterprise Security** - SOC2 compliant with encryption
- 📱 **Responsive Design** - Works on all devices

## 🏗️ Architecture

```
Marketing Frontend → Stripe Payment → User Dashboard → Axie Studio Workflow
     (Wasp)            (Wasp)         (Wasp)           (Your Langflow)
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- Stripe account
- Your existing Langflow/Axie Studio backend

### Installation

1. **Clone and setup**
   ```bash
   cd axie-studio-business
   npm install
   ```

2. **Configure environment variables**
   ```bash
   cp .env.server.example .env.server
   cp .env.client.example .env.client
   ```

3. **Update your environment files with:**
   - Database connection string
   - Stripe API keys and price IDs
   - Langflow/Axie Studio backend URL and API key
   - JWT secret
   - Email provider settings

4. **Setup database**
   ```bash
   wasp db migrate-dev
   ```

5. **Start development server**
   ```bash
   wasp start
   ```

Visit `http://localhost:3000` to see your Axie Studio business platform!

## 📋 Environment Configuration

### Server Environment (.env.server)

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/axie_studio"

# Stripe
STRIPE_API_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
STRIPE_STARTER_PRICE_ID="price_..."
STRIPE_PRO_PRICE_ID="price_..."
STRIPE_ENTERPRISE_PRICE_ID="price_..."

# Axie Studio Backend
LANGFLOW_BASE_URL="https://langflow-tv34o.ondigitalocean.app"
LANGFLOW_API_KEY="entrance"

# Security
JWT_SECRET="your-super-secret-jwt-key"

# Email
SENDGRID_API_KEY="SG...."
SENDGRID_FROM_EMAIL="<EMAIL>"

# URLs
CLIENT_URL="http://localhost:3000"
SERVER_URL="http://localhost:3001"
```

### Client Environment (.env.client)

```bash
REACT_APP_STRIPE_PUBLISHABLE_KEY="pk_test_..."
REACT_APP_API_BASE_URL="http://localhost:3001"
```

## 💳 Stripe Setup

1. **Create products in Stripe Dashboard:**
   - Starter Plan: $29/month
   - Pro Plan: $99/month  
   - Enterprise Plan: Custom pricing

2. **Configure webhooks:**
   - Endpoint: `https://yourdomain.com/payments-webhook`
   - Events: `checkout.session.completed`, `customer.subscription.*`, `invoice.payment_*`

3. **Update price IDs in environment variables**

## 🔧 Customization

### Branding
Update `src/client/config/brand.ts` to customize:
- Colors and gradients
- Feature descriptions
- Pricing plans
- Company information

### Integrations
The platform integrates with your existing Langflow backend through:
- API proxying (`/api/axie/*`)
- User authentication and authorization
- Credit/usage tracking
- Workflow management

## 📊 Business Features

### Subscription Plans

- **Starter ($29/month)**
  - 10 active workflows
  - 2,500 executions/month
  - 5GB storage
  - Email support

- **Pro ($99/month)**
  - 100 active workflows
  - 25,000 executions/month
  - 50GB storage
  - Priority support
  - Advanced analytics

- **Enterprise (Custom)**
  - Unlimited workflows
  - Unlimited executions
  - 500GB+ storage
  - Dedicated support
  - Custom integrations

### User Journey

1. 🌐 **Landing Page** - User discovers Axie Studio
2. 💰 **Pricing** - User selects plan
3. 💳 **Payment** - Stripe checkout
4. ✅ **Success** - Account activation
5. 🏠 **Dashboard** - Account management
6. 🔧 **Workflows** - Visual builder access

## 🚀 Deployment

### Using Wasp CLI
```bash
wasp build
wasp deploy
```

### Manual Deployment
1. Build the application: `wasp build`
2. Deploy server to your hosting provider
3. Deploy client to Vercel/Netlify
4. Configure environment variables
5. Set up database migrations

## 🔒 Security

- JWT-based authentication
- API rate limiting by subscription tier
- Secure Stripe webhook verification
- Environment variable protection
- CORS configuration
- Input validation and sanitization

## 📈 Analytics & Monitoring

Track key metrics:
- User signups and conversions
- Subscription upgrades/downgrades
- Workflow creation and execution
- Credit usage patterns
- Support ticket volume

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/axiestudio)
- 📚 Docs: [docs.axiestudio.com](https://docs.axiestudio.com)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ using Wasp, React, and the power of AI**

🎉 **Ready to revolutionize AI workflow automation? Let's build something amazing together!**
