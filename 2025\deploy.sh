#!/bin/bash

# Axie Studio Deployment Script
# This script helps deploy your Axie Studio business platform

set -e

echo "🚀 Deploying Axie Studio Business Platform..."

# Check if wasp is installed
if ! command -v wasp &> /dev/null; then
    echo "❌ Wasp CLI not found. Please install Wasp first:"
    echo "   curl -sSL https://get.wasp.sh/installer.sh | sh"
    exit 1
fi

# Check if environment files exist
if [ ! -f ".env.server" ]; then
    echo "❌ .env.server file not found. Please copy from .env.server.example and configure."
    exit 1
fi

if [ ! -f ".env.client" ]; then
    echo "❌ .env.client file not found. Please copy from .env.client.example and configure."
    exit 1
fi

echo "✅ Environment files found"

# Build the application
echo "📦 Building application..."
wasp build

echo "✅ Build completed successfully"

# Run database migrations
echo "🗄️ Running database migrations..."
wasp db migrate-deploy

echo "✅ Database migrations completed"

# Deploy the application
echo "🌐 Deploying to production..."
wasp deploy

echo "🎉 Deployment completed successfully!"
echo ""
echo "🔗 Your Axie Studio platform should now be live!"
echo "📊 Don't forget to:"
echo "   - Configure your Stripe webhooks"
echo "   - Set up your domain DNS"
echo "   - Test the payment flow"
echo "   - Monitor the application logs"
echo ""
echo "🎯 Happy building with Axie Studio!"
