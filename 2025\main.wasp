app AxieStudioBusiness {
  wasp: {
    version: "^0.17.0"
  },
  title: "Axie Studio - AI Workflow Platform",
  head: [
    "<meta property='og:type' content='website' />",
    "<meta property='og:title' content='Axie Studio - Build AI Workflows Visually' />",
    "<meta property='og:description' content='The most powerful visual AI workflow builder for creators, developers, and businesses' />",
    "<meta name='description' content='Build AI workflows that actually work. Axie Studio empowers creators and businesses to build, deploy, and scale AI workflows without writing code.' />"
  ],

  auth: {
    userEntity: User,
    methods: {
      email: {
        fromField: {
          name: "Axie Studio",
          email: "<EMAIL>"
        },
        emailVerification: {
          clientRoute: EmailVerificationRoute,
        },
        passwordReset: {
          clientRoute: PasswordResetRoute,
        },
      },
      google: {},
    },
    onAuthFailedRedirectTo: "/login",
    onAuthSucceededRedirectTo: "/dashboard"
  },

  emailSender: {
    provider: SendGrid,
    defaultFrom: {
      name: "Axie Studio",
      email: "<EMAIL>"
    },
  },

  db: {
    system: PostgreSQL,
  },

  client: {
    rootComponent: import { Layout } from "@src/client/Layout",
  },

  server: {
    setupFn: import setup from "@src/server/setup",
    middlewareConfigFn: import { serverMiddlewareFn } from "@src/server/middleware"
  },
}

// ===== ROUTES & PAGES =====

// Marketing pages (public)
route LandingPageRoute { path: "/", to: LandingPage }
page LandingPage {
  component: import { LandingPage } from "@src/client/pages/LandingPage"
}

route PricingRoute { path: "/pricing", to: PricingPage }
page PricingPage {
  component: import { PricingPage } from "@src/client/pages/PricingPage"
}

route AboutRoute { path: "/about", to: AboutPage }
page AboutPage {
  component: import { AboutPage } from "@src/client/pages/AboutPage"
}

// Auth pages
route LoginRoute { path: "/login", to: LoginPage }
page LoginPage {
  component: import { LoginPage } from "@src/client/pages/auth/LoginPage"
}

route SignupRoute { path: "/signup", to: SignupPage }
page SignupPage {
  component: import { SignupPage } from "@src/client/pages/auth/SignupPage"
}

route EmailVerificationRoute { path: "/email-verification", to: EmailVerificationPage }
page EmailVerificationPage {
  component: import { EmailVerificationPage } from "@src/client/pages/auth/EmailVerificationPage"
}

route PasswordResetRoute { path: "/password-reset", to: PasswordResetPage }
page PasswordResetPage {
  component: import { PasswordResetPage } from "@src/client/pages/auth/PasswordResetPage"
}

// Payment success redirect
route PaymentSuccessRoute { path: "/payment-success", to: PaymentSuccessPage }
page PaymentSuccessPage {
  component: import { PaymentSuccessPage } from "@src/client/pages/PaymentSuccessPage",
  authRequired: true
}

// User dashboard (after payment)
route DashboardRoute { path: "/dashboard", to: DashboardPage }
page DashboardPage {
  component: import { DashboardPage } from "@src/client/pages/DashboardPage",
  authRequired: true
}

// Axie Studio workflow integration
route WorkflowRoute { path: "/workflow", to: WorkflowPage }
page WorkflowPage {
  component: import { WorkflowPage } from "@src/client/pages/WorkflowPage",
  authRequired: true
}

// Account management
route AccountRoute { path: "/account", to: AccountPage }
page AccountPage {
  component: import { AccountPage } from "@src/client/pages/AccountPage",
  authRequired: true
}

// ===== ENTITIES =====

entity User {=psl
  id                        String    @id @default(cuid())
  email                     String?   @unique
  username                  String?   @unique
  createdAt                 DateTime  @default(now())
  updatedAt                 DateTime  @updatedAt
  isEmailVerified           Boolean   @default(false)
  emailVerificationSentAt   DateTime?
  passwordResetSentAt       DateTime?
  
  // Subscription & Payment fields
  subscriptionStatus        String?   // 'active', 'past_due', 'canceled', etc.
  subscriptionPlan          String?   // 'starter', 'pro', 'enterprise'
  stripeId                  String?   @unique
  checkoutSessionId         String?
  subscriptionId            String?
  
  // Axie Studio specific fields
  axieStudioApiKey          String?   // Generated API key for Axie Studio access
  workflowsCreated          Int       @default(0)
  lastWorkflowAccess        DateTime?
  
  // Usage tracking
  creditsUsed               Int       @default(0)
  creditsTotal              Int       @default(0)
  
  // Relations
  workflows                 Workflow[]
psl=}

entity Workflow {=psl
  id          String   @id @default(cuid())
  name        String
  description String?
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Axie Studio integration
  langflowId  String?  // ID in your Langflow backend
  isActive    Boolean  @default(true)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
psl=}

// ===== OPERATIONS =====

// User operations
query getUserStats {
  fn: import { getUserStats } from "@src/server/operations/user",
  entities: [User, Workflow]
}

query getUserWorkflows {
  fn: import { getUserWorkflows } from "@src/server/operations/workflows",
  entities: [Workflow]
}

action updateUserProfile {
  fn: import { updateUserProfile } from "@src/server/operations/user",
  entities: [User]
}

// Workflow operations
action createWorkflow {
  fn: import { createWorkflow } from "@src/server/operations/workflows",
  entities: [User, Workflow]
}

action deleteWorkflow {
  fn: import { deleteWorkflow } from "@src/server/operations/workflows",
  entities: [Workflow]
}

// Payment operations
action createCheckoutSession {
  fn: import { createCheckoutSession } from "@src/server/operations/payments",
  entities: [User]
}

// ===== APIs =====

// Stripe webhook for payment processing
api paymentsWebhook {
  fn: import { paymentsWebhook } from "@src/server/payments",
  httpRoute: (POST, "/payments-webhook"),
  auth: false
}

// Axie Studio API proxy
api axieStudioProxy {
  fn: import { axieStudioProxy } from "@src/server/axieStudio",
  httpRoute: (ALL, "/api/axie/*"),
  auth: true,
  entities: [User]
}

// API namespace for CORS
apiNamespace axieStudioApi {
  middlewareConfigFn: import { corsMiddleware } from "@src/server/middleware",
  path: "/api/axie"
}

// ===== JOBS =====

job emailChecker {
  executor: PgBoss,
  perform: {
    fn: import { checkAndQueueEmails } from "@src/server/workers/emailChecker"
  },
  schedule: {
    cron: "0 7 * * 1" // every Monday at 7am
  }
}
