{"version": 3, "file": "dom.iterable.js", "sourceRoot": "", "sources": ["../../src/lib/dom.iterable.ts"], "names": [], "mappings": ";AAAA,wCAAwC;AACxC,gCAAgC;AAChC,mEAAmE;AACnE,oDAAoD;;;AAGpD,+CAAqC;AAExB,QAAA,YAAY,GAAG;IAC1B,UAAU,EAAE,kBAAI;IAChB,aAAa,EAAE,kBAAI;IACnB,gBAAgB,EAAE,kBAAI;IACtB,gBAAgB,EAAE,kBAAI;IACtB,eAAe,EAAE,kBAAI;IACrB,WAAW,EAAE,kBAAI;IACjB,mBAAmB,EAAE,kBAAI;IACzB,iBAAiB,EAAE,kBAAI;IACvB,gBAAgB,EAAE,kBAAI;IACtB,KAAK,EAAE,kBAAI;IACX,UAAU,EAAE,kBAAI;IAChB,uBAAuB,EAAE,kBAAI;IAC7B,WAAW,EAAE,kBAAI;IACjB,aAAa,EAAE,kBAAI;IACnB,YAAY,EAAE,kBAAI;IAClB,oBAAoB,EAAE,kBAAI;IAC1B,WAAW,EAAE,kBAAI;IACjB,QAAQ,EAAE,kBAAI;IACd,WAAW,EAAE,kBAAI;IACjB,QAAQ,EAAE,kBAAI;IACd,iBAAiB,EAAE,kBAAI;IACvB,kBAAkB,EAAE,kBAAI;IACxB,gBAAgB,EAAE,kBAAI;IACtB,eAAe,EAAE,kBAAI;IACrB,iBAAiB,EAAE,kBAAI;IACvB,OAAO,EAAE,kBAAI;IACb,WAAW,EAAE,kBAAI;IACjB,cAAc,EAAE,kBAAI;IACpB,YAAY,EAAE,kBAAI;IAClB,UAAU,EAAE,kBAAI;IAChB,aAAa,EAAE,kBAAI;IACnB,iBAAiB,EAAE,kBAAI;IACvB,SAAS,EAAE,kBAAI;IACf,YAAY,EAAE,kBAAI;IAClB,aAAa,EAAE,kBAAI;IACnB,YAAY,EAAE,kBAAI;IAClB,SAAS,EAAE,kBAAI;IACf,QAAQ,EAAE,kBAAI;IACd,UAAU,EAAE,kBAAI;IAChB,MAAM,EAAE,kBAAI;IACZ,WAAW,EAAE,kBAAI;IACjB,iBAAiB,EAAE,kBAAI;IACvB,cAAc,EAAE,kBAAI;IACpB,aAAa,EAAE,kBAAI;IACnB,aAAa,EAAE,kBAAI;IACnB,YAAY,EAAE,kBAAI;IAClB,aAAa,EAAE,kBAAI;IACnB,gBAAgB,EAAE,kBAAI;IACtB,gBAAgB,EAAE,kBAAI;IACtB,uBAAuB,EAAE,kBAAI;IAC7B,2BAA2B,EAAE,kBAAI;IACjC,wBAAwB,EAAE,kBAAI;IAC9B,cAAc,EAAE,kBAAI;IACpB,YAAY,EAAE,kBAAI;IAClB,gBAAgB,EAAE,kBAAI;IACtB,aAAa,EAAE,kBAAI;IACnB,SAAS,EAAE,kBAAI;IACf,eAAe,EAAE,kBAAI;IACrB,kBAAkB,EAAE,kBAAI;IACxB,gBAAgB,EAAE,kBAAI;IACtB,0BAA0B,EAAE,kBAAI;IAChC,+BAA+B,EAAE,kBAAI;IACrC,yBAAyB,EAAE,kBAAI;IAC/B,8BAA8B,EAAE,kBAAI;CACS,CAAC"}