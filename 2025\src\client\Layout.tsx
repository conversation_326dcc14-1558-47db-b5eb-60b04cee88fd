import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { Navigation } from './components/Navigation';
import { Footer } from './components/Footer';

export function Layout() {
  const location = useLocation();
  
  // Pages that should have full-screen layout (no nav/footer)
  const fullScreenPages = ['/workflow'];
  const isFullScreen = fullScreenPages.includes(location.pathname);
  
  // Auth pages that should have minimal layout
  const authPages = ['/login', '/signup', '/email-verification', '/password-reset'];
  const isAuthPage = authPages.includes(location.pathname);

  if (isFullScreen) {
    return (
      <>
        <Outlet />
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1F2937',
              color: '#F9FAFB',
            },
          }}
        />
      </>
    );
  }

  if (isAuthPage) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Outlet />
        <Toaster 
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1F2937',
              color: '#F9FAFB',
            },
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <Navigation />
      <main className="flex-1">
        <Outlet />
      </main>
      <Footer />
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#1F2937',
            color: '#F9FAFB',
          },
        }}
      />
    </div>
  );
}
