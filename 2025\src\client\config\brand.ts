export const AXIE_STUDIO_BRAND = {
  name: "Axie Studio",
  tagline: "Build AI Workflows Visually",
  description: "The most powerful visual AI workflow builder for creators, developers, and businesses",
  
  colors: {
    primary: "#8B5CF6", // Purple
    secondary: "#06B6D4", // Cyan
    accent: "#F59E0B", // Amber
    success: "#10B981", // Emerald
    warning: "#F59E0B", // Amber
    error: "#EF4444", // Red
    dark: "#1F2937", // Gray-800
    light: "#F9FAFB", // Gray-50
  },
  
  gradients: {
    primary: "linear-gradient(135deg, #8B5CF6 0%, #06B6D4 100%)",
    hero: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    card: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
  },
  
  features: {
    starter: {
      workflows: 10,
      executions: 2500,
      storage: "5GB",
      support: "Community",
      price: 29,
    },
    pro: {
      workflows: 100,
      executions: 25000,
      storage: "50GB", 
      support: "Priority",
      price: 99,
    },
    enterprise: {
      workflows: "Unlimited",
      executions: "Unlimited",
      storage: "500GB",
      support: "Dedicated",
      price: "Custom",
    }
  }
};

export const AXIE_STUDIO_COPY = {
  hero: {
    title: "Build AI Workflows That Actually Work",
    subtitle: "Axie Studio empowers creators and businesses to build, deploy, and scale AI workflows without writing a single line of code.",
    cta: "Start Building for Free"
  },
  
  features: [
    {
      icon: "🎨",
      title: "Visual Workflow Builder",
      description: "Drag, drop, and connect AI components to create powerful workflows in minutes, not hours."
    },
    {
      icon: "🚀",
      title: "One-Click Deployment",
      description: "Deploy your AI workflows instantly to our global infrastructure with enterprise-grade reliability."
    },
    {
      icon: "🔗",
      title: "300+ Integrations",
      description: "Connect to your favorite tools: OpenAI, Anthropic, Google, Slack, Discord, and hundreds more."
    },
    {
      icon: "📊",
      title: "Real-Time Analytics",
      description: "Monitor performance, track usage, and optimize your workflows with detailed insights."
    },
    {
      icon: "🛡️",
      title: "Enterprise Security",
      description: "SOC2 compliant with end-to-end encryption, role-based access, and audit logs."
    },
    {
      icon: "⚡",
      title: "Lightning Fast",
      description: "Sub-second response times with auto-scaling infrastructure that grows with your needs."
    }
  ],
  
  testimonials: [
    {
      name: "Sarah Chen",
      role: "AI Product Manager at TechCorp",
      content: "Axie Studio transformed how we build AI features. What used to take weeks now takes hours.",
      avatar: "👩‍💼"
    },
    {
      name: "Marcus Rodriguez", 
      role: "Founder of AutomateAI",
      content: "The visual interface is incredible. My non-technical team can now build complex AI workflows.",
      avatar: "👨‍💻"
    }
  ],

  useCases: [
    {
      title: "Content Creation",
      description: "Generate blogs, social posts, and marketing copy",
      icon: "✍️",
      examples: ["Blog writing", "Social media", "Email campaigns"]
    },
    {
      title: "Data Analysis", 
      description: "Process and analyze large datasets with AI",
      icon: "📊",
      examples: ["Report generation", "Data insights", "Trend analysis"]
    },
    {
      title: "Customer Support",
      description: "Automate responses and ticket routing",
      icon: "🎧",
      examples: ["Chatbots", "Ticket routing", "FAQ automation"]
    },
    {
      title: "Research & Insights",
      description: "Gather and synthesize information from multiple sources",
      icon: "🔍",
      examples: ["Market research", "Competitive analysis", "Trend monitoring"]
    }
  ]
};

export const AXIE_STUDIO_URLS = {
  website: "https://axiestudio.com",
  docs: "https://docs.axiestudio.com",
  blog: "https://blog.axiestudio.com",
  discord: "https://discord.gg/axiestudio",
  twitter: "https://twitter.com/axiestudio",
  github: "https://github.com/axiestudio",
  support: "mailto:<EMAIL>",
  sales: "mailto:<EMAIL>"
};
