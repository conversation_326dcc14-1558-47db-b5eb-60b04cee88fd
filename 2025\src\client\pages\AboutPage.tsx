import React from 'react';
import { Link } from 'react-router-dom';
import { AXIE_STUDIO_BRAND, AXIE_STUDIO_COPY } from '../config/brand';

export function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-cyan-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              About Axie Studio
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              We're building the future of AI workflow automation, making it accessible to everyone.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
                <p className="text-gray-600 mb-6">
                  At Axie Studio, we believe that AI should be accessible to everyone, not just developers. 
                  Our mission is to democratize AI workflow creation through intuitive visual tools that 
                  empower creators, businesses, and innovators to build powerful automations without writing code.
                </p>
                <p className="text-gray-600">
                  We're passionate about bridging the gap between complex AI capabilities and practical, 
                  real-world applications that drive meaningful results.
                </p>
              </div>
              <div className="bg-gradient-to-br from-purple-100 to-cyan-100 rounded-2xl p-8">
                <div className="text-6xl mb-4">🎯</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Our Vision</h3>
                <p className="text-gray-700">
                  A world where anyone can harness the power of AI to solve problems, 
                  automate tasks, and create value through simple, visual workflows.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Values</h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: "🚀",
                  title: "Innovation First",
                  description: "We constantly push the boundaries of what's possible with AI workflow automation."
                },
                {
                  icon: "🤝",
                  title: "User-Centric",
                  description: "Every feature we build starts with understanding our users' real needs and challenges."
                },
                {
                  icon: "🔒",
                  title: "Security & Privacy",
                  description: "We protect your data with enterprise-grade security and transparent privacy practices."
                },
                {
                  icon: "🌍",
                  title: "Accessibility",
                  description: "AI should be accessible to everyone, regardless of technical background or resources."
                },
                {
                  icon: "⚡",
                  title: "Performance",
                  description: "We obsess over speed, reliability, and scalability in everything we build."
                },
                {
                  icon: "🎨",
                  title: "Simplicity",
                  description: "Complex problems deserve elegant, simple solutions that anyone can understand."
                }
              ].map((value, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm">
                  <div className="text-4xl mb-4">{value.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Built by AI Enthusiasts</h2>
            <p className="text-xl text-gray-600 mb-12">
              Our team combines deep AI expertise with a passion for creating tools that make a difference.
            </p>
            
            <div className="bg-gradient-to-r from-purple-600 to-cyan-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Join Our Journey</h3>
              <p className="text-purple-100 mb-6">
                We're always looking for talented individuals who share our vision of making AI accessible to everyone.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="bg-white text-purple-600 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  View Open Positions
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="border-2 border-white text-white hover:bg-white hover:text-purple-600 px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Get in Touch
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Start Building?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of creators and businesses already using Axie Studio.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/signup"
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors"
            >
              Start Free Trial
            </Link>
            <Link
              to="/pricing"
              className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-xl font-semibold transition-colors"
            >
              View Pricing
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
