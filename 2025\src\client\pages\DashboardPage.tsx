import React, { useState } from 'react';
import { useQuery } from 'wasp/client/operations';
import { getUserStats, getUserWorkflows } from 'wasp/client/operations';
import { Link } from 'react-router-dom';
import { AXIE_STUDIO_BRAND } from '../config/brand';

export function DashboardPage({ user }: { user: any }) {
  const { data: userStats, isLoading: statsLoading } = useQuery(getUserStats);
  const { data: workflows, isLoading: workflowsLoading } = useQuery(getUserWorkflows);
  const [activeTab, setActiveTab] = useState('overview');

  if (statsLoading || workflowsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your Axie Studio dashboard...</p>
        </div>
      </div>
    );
  }

  const canAccessWorkflows = user.subscriptionStatus === 'active';
  const stats = userStats?.stats || {};

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">A</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Axie Studio</h1>
                <p className="text-gray-600">Welcome back, {user.email?.split('@')[0] || 'Creator'}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-gray-500">Current Plan</div>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold capitalize text-gray-900">
                    {user.subscriptionPlan || 'Free'}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    user.subscriptionStatus === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {user.subscriptionStatus || 'inactive'}
                  </span>
                </div>
              </div>
              
              {canAccessWorkflows ? (
                <Link
                  to="/workflow"
                  className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  🚀 Open Studio
                </Link>
              ) : (
                <Link
                  to="/pricing"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-xl font-medium"
                >
                  Upgrade Plan
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats Bar */}
      {canAccessWorkflows && (
        <div className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white">
          <div className="container mx-auto px-4 py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{stats.activeWorkflows || 0}</div>
                <div className="text-purple-100 text-sm">Active Workflows</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.executionsThisMonth || 0}</div>
                <div className="text-purple-100 text-sm">Executions Today</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.creditsRemaining || 0}</div>
                <div className="text-purple-100 text-sm">Credits Left</div>
              </div>
              <div>
                <div className="text-2xl font-bold">99.9%</div>
                <div className="text-purple-100 text-sm">Uptime</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {!canAccessWorkflows ? (
          <UpgradePrompt />
        ) : (
          <ActiveDashboard stats={stats} workflows={workflows || []} user={user} />
        )}
      </div>
    </div>
  );
}

function UpgradePrompt() {
  return (
    <div className="text-center py-16">
      <div className="max-w-2xl mx-auto">
        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-cyan-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-4xl">🚀</span>
        </div>
        
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Ready to Start Building AI Workflows?
        </h2>
        
        <p className="text-xl text-gray-600 mb-8">
          Upgrade to access Axie Studio's powerful visual workflow builder and start automating with AI.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {[
            { icon: "🎨", title: "Visual Builder", desc: "Drag & drop interface" },
            { icon: "🔗", title: "300+ Integrations", desc: "Connect any service" },
            { icon: "⚡", title: "Instant Deploy", desc: "One-click publishing" }
          ].map((feature, i) => (
            <div key={i} className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-3xl mb-2">{feature.icon}</div>
              <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
              <p className="text-gray-600 text-sm">{feature.desc}</p>
            </div>
          ))}
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/pricing"
            className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
          >
            View Pricing Plans
          </Link>
          <button className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-xl font-semibold">
            Watch Demo
          </button>
        </div>
      </div>
    </div>
  );
}

function ActiveDashboard({ stats, workflows, user }: { stats: any; workflows: any[]; user: any }) {
  return (
    <div className="space-y-8">
      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h2>
        
        <div className="space-y-4">
          {[
            { action: "Workflow executed", name: "Customer Support Bot", time: "2 minutes ago", status: "success" },
            { action: "Workflow created", name: "Content Generator", time: "1 hour ago", status: "info" },
            { action: "Integration added", name: "Slack Connector", time: "3 hours ago", status: "info" },
            { action: "Workflow executed", name: "Data Analyzer", time: "5 hours ago", status: "success" }
          ].map((activity, i) => (
            <div key={i} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.status === 'success' ? 'bg-green-500' : 'bg-blue-500'
                }`}></div>
                <div>
                  <span className="text-gray-900 font-medium">{activity.action}</span>
                  <span className="text-purple-600 ml-1">{activity.name}</span>
                </div>
              </div>
              <span className="text-gray-500 text-sm">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Workflows Grid */}
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">Your Workflows</h2>
          <Link
            to="/workflow"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium"
          >
            + Create Workflow
          </Link>
        </div>

        {workflows.length === 0 ? (
          <div className="bg-white rounded-xl p-12 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-cyan-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎨</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Create Your First Workflow</h3>
            <p className="text-gray-600 mb-6">Start building AI-powered automations with our visual editor.</p>
            <Link
              to="/workflow"
              className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-lg font-medium"
            >
              🚀 Start Building
            </Link>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workflows.map((workflow) => (
              <div key={workflow.id} className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-semibold text-lg text-gray-900">{workflow.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    workflow.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {workflow.isActive ? '🟢 Active' : '⚪ Inactive'}
                  </span>
                </div>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {workflow.description || "No description provided"}
                </p>
                
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">
                    Updated {new Date(workflow.updatedAt).toLocaleDateString()}
                  </span>
                  <Link
                    to={`/workflow?id=${workflow.id}`}
                    className="text-purple-600 hover:text-purple-800 font-medium"
                  >
                    Edit →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
