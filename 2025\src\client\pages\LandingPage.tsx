import React from 'react';
import { Link } from 'react-router-dom';
import { AXIE_STUDIO_BRAND, AXIE_STUDIO_COPY } from '../config/brand';

export function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
        <div className="relative container mx-auto px-4 py-24 lg:py-32">
          <div className="text-center max-w-5xl mx-auto">
            <div className="mb-8">
              <span className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold bg-gradient-to-r from-indigo-100 to-sky-100 text-indigo-800 border border-indigo-200">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                Trusted by Fortune 500 Companies • SOC2 Compliant • 99.99% Uptime
              </span>
            </div>

            <h1 className="text-6xl lg:text-8xl font-bold text-slate-900 mb-8 leading-tight tracking-tight">
              Enterprise AI
              <span className="block bg-gradient-to-r from-indigo-600 via-blue-600 to-sky-600 bg-clip-text text-transparent">
                Workflow Platform
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-slate-600 mb-10 max-w-4xl mx-auto leading-relaxed font-medium">
              {AXIE_STUDIO_COPY.hero.subtitle}
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link
                to="/signup"
                className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white px-10 py-5 rounded-2xl text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border border-indigo-500"
              >
                Start Enterprise Trial
              </Link>
              <button className="flex items-center space-x-3 text-slate-700 hover:text-indigo-600 px-10 py-5 rounded-2xl border-2 border-slate-200 hover:border-indigo-200 transition-all duration-300 bg-white/80 backdrop-blur-sm font-semibold">
                <span className="w-6 h-6 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center text-white text-sm">▶</span>
                <span>Watch Platform Demo</span>
              </button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center mb-16">
              <div>
                <div className="text-3xl font-bold text-slate-900">{AXIE_STUDIO_BRAND.stats.users}</div>
                <div className="text-sm text-slate-600 font-medium">Enterprise Users</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-slate-900">{AXIE_STUDIO_BRAND.stats.workflows}</div>
                <div className="text-sm text-slate-600 font-medium">Workflows Deployed</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-slate-900">{AXIE_STUDIO_BRAND.stats.uptime}</div>
                <div className="text-sm text-slate-600 font-medium">Uptime SLA</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-slate-900">{AXIE_STUDIO_BRAND.stats.countries}</div>
                <div className="text-sm text-slate-600 font-medium">Countries</div>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-8 text-sm text-slate-500">
              <div className="flex items-center space-x-2">
                <span className="text-emerald-500">✓</span>
                <span>30-day free trial</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-emerald-500">✓</span>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-emerald-500">✓</span>
                <span>Dedicated success manager</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-16 bg-white border-t border-slate-200">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <p className="text-slate-600 font-medium mb-8">Trusted by industry leaders worldwide</p>
            <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center opacity-60">
              {['Microsoft', 'Salesforce', 'Google', 'Amazon', 'IBM', 'Oracle'].map((company, i) => (
                <div key={i} className="text-center">
                  <div className="w-16 h-16 bg-slate-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-2xl font-bold text-slate-400">{company.charAt(0)}</span>
                  </div>
                  <span className="text-sm font-medium text-slate-500">{company}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo Preview */}
      <section className="py-24 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              See Enterprise AI Automation in Action
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Watch how Fortune 500 companies are transforming their operations with Axie Studio's enterprise platform
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-slate-200">
              <div className="bg-slate-900 px-8 py-6 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="ml-6 text-slate-400 text-sm font-mono">enterprise.axiestudio.com</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-emerald-400 text-sm font-medium">🔒 SOC2 Compliant</span>
                  <span className="text-emerald-400 text-sm font-medium">🛡️ Enterprise Security</span>
                </div>
              </div>

              <div className="aspect-video bg-gradient-to-br from-indigo-50 via-blue-50 to-sky-50 flex items-center justify-center relative">
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1" fill-opacity="0.1"%3E%3Cpath d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
                <div className="text-center z-10">
                  <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 mx-auto shadow-xl border border-slate-200">
                    <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-lg">▶</span>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">Enterprise Platform Demo</h3>
                  <p className="text-slate-600 mb-6 max-w-md mx-auto">See how global enterprises are automating complex workflows with AI</p>
                  <button className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                    Watch Demo (3 min)
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Enterprise-Grade AI Automation Platform
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Built for Fortune 500 companies with the security, scalability, and reliability your enterprise demands
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {AXIE_STUDIO_COPY.features.map((feature, index) => (
              <div key={index} className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 hover:border-indigo-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-blue-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-3xl">{feature.icon}</span>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4 group-hover:text-indigo-900 transition-colors">{feature.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Additional Enterprise Features */}
          <div className="mt-20 bg-gradient-to-r from-slate-50 to-blue-50 rounded-3xl p-12 border border-slate-200">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-slate-900 mb-4">Enterprise Security & Compliance</h3>
              <p className="text-lg text-slate-600 max-w-3xl mx-auto">
                Meet the highest security standards with comprehensive compliance and governance features
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { icon: "🛡️", title: "SOC2 Type II", desc: "Certified security controls" },
                { icon: "🔒", title: "GDPR Compliant", desc: "Data privacy protection" },
                { icon: "🏢", title: "SSO & SAML", desc: "Enterprise authentication" },
                { icon: "📋", title: "Audit Trails", desc: "Complete activity logging" }
              ].map((item, i) => (
                <div key={i} className="text-center">
                  <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <span className="text-2xl">{item.icon}</span>
                  </div>
                  <h4 className="font-bold text-slate-900 mb-2">{item.title}</h4>
                  <p className="text-sm text-slate-600">{item.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Built for Every AI Use Case
            </h2>
            <p className="text-xl text-gray-600">
              From content creation to data analysis, Axie Studio powers it all
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {AXIE_STUDIO_COPY.useCases.map((useCase, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <div className="text-4xl mb-4">{useCase.icon}</div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{useCase.title}</h3>
                <p className="text-gray-600 text-sm mb-4">{useCase.description}</p>
                <ul className="space-y-1">
                  {useCase.examples.map((example, i) => (
                    <li key={i} className="text-xs text-gray-500 flex items-center">
                      <span className="text-purple-500 mr-2">•</span>
                      {example}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Success Stories */}
      <section className="py-24 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Trusted by Enterprise Leaders
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              See how Fortune 500 companies are transforming their operations with Axie Studio
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {AXIE_STUDIO_COPY.testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-xl border border-slate-200 relative">
                <div className="absolute top-0 left-8 transform -translate-y-1/2">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <span className="text-2xl text-white">{testimonial.logo}</span>
                  </div>
                </div>

                <div className="pt-8">
                  <div className="flex items-center mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-full flex items-center justify-center mr-4">
                      <span className="text-2xl">{testimonial.avatar}</span>
                    </div>
                    <div>
                      <div className="font-bold text-slate-900 text-lg">{testimonial.name}</div>
                      <div className="text-sm text-slate-600 font-medium">{testimonial.role}</div>
                      <div className="text-xs text-indigo-600 font-semibold">{testimonial.company}</div>
                    </div>
                  </div>

                  <blockquote className="text-slate-700 leading-relaxed mb-6">
                    "{testimonial.content}"
                  </blockquote>

                  <div className="flex items-center text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-lg">★</span>
                    ))}
                    <span className="ml-2 text-sm text-slate-600 font-medium">5.0 Enterprise Rating</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* ROI Statistics */}
          <div className="mt-20 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-3xl p-12 text-white">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">Proven Enterprise ROI</h3>
              <p className="text-xl text-indigo-100 max-w-3xl mx-auto">
                Our enterprise customers see measurable results within the first quarter
              </p>
            </div>

            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">85%</div>
                <div className="text-indigo-100">Faster Deployment</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">$2.3M</div>
                <div className="text-indigo-100">Average Annual Savings</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">300%</div>
                <div className="text-indigo-100">Efficiency Increase</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">6 Months</div>
                <div className="text-indigo-100">Average Payback Period</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enterprise CTA Section */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-indigo-900 to-blue-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Ready to Transform Your
            <span className="block bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Enterprise Operations?
            </span>
          </h2>
          <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
            Join Fortune 500 companies already automating their most critical business processes with Axie Studio's enterprise AI platform
          </p>

          <div className="flex flex-col lg:flex-row gap-6 justify-center items-center mb-12">
            <Link
              to="/signup"
              className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:-translate-y-1 border border-blue-400"
            >
              Start Enterprise Trial
            </Link>
            <button className="border-2 border-slate-400 text-white hover:bg-white hover:text-slate-900 px-10 py-5 rounded-2xl text-lg font-semibold transition-all duration-300 backdrop-blur-sm">
              Schedule Demo Call
            </button>
            <Link
              to="/pricing"
              className="text-slate-300 hover:text-white px-8 py-5 rounded-2xl text-lg font-semibold transition-all duration-300 underline underline-offset-4"
            >
              View Enterprise Pricing
            </Link>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-emerald-400 text-lg mb-2">✓</div>
              <div className="text-white font-semibold">30-Day Free Trial</div>
              <div className="text-slate-400 text-sm">Full enterprise features</div>
            </div>
            <div className="text-center">
              <div className="text-emerald-400 text-lg mb-2">✓</div>
              <div className="text-white font-semibold">Dedicated Success Manager</div>
              <div className="text-slate-400 text-sm">White-glove onboarding</div>
            </div>
            <div className="text-center">
              <div className="text-emerald-400 text-lg mb-2">✓</div>
              <div className="text-white font-semibold">Enterprise SLA</div>
              <div className="text-slate-400 text-sm">99.99% uptime guarantee</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
