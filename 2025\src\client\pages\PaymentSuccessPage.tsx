import React, { useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { AXIE_STUDIO_BRAND } from '../config/brand';

export function PaymentSuccessPage({ user }: { user: any }) {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    // You could verify the session here if needed
    console.log('Payment successful for session:', sessionId);
  }, [sessionId]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-4xl">🎉</span>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to Axie Studio!
          </h1>
          
          <p className="text-gray-600 mb-6">
            Your subscription is now active. You can start building AI workflows right away!
          </p>
          
          <div className="bg-gradient-to-r from-purple-50 to-cyan-50 border border-purple-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-purple-900 mb-2">What's next?</h3>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>✓ Access the visual workflow builder</li>
              <li>✓ Connect to 300+ integrations</li>
              <li>✓ Deploy workflows instantly</li>
              <li>✓ Get priority support</li>
            </ul>
          </div>
          
          <div className="space-y-3">
            <Link
              to="/workflow"
              className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 block"
            >
              🚀 Start Building Workflows
            </Link>
            
            <Link
              to="/dashboard"
              className="w-full border border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-6 rounded-xl font-semibold transition-colors block"
            >
              Go to Dashboard
            </Link>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Need help getting started? Check out our{' '}
              <a href="#" className="text-purple-600 hover:text-purple-500">
                quick start guide
              </a>{' '}
              or{' '}
              <a href="#" className="text-purple-600 hover:text-purple-500">
                contact support
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
