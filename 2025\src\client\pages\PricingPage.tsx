import React, { useState } from 'react';
import { useAuth } from '../../wasp/client/auth';
import { createCheckoutSession } from '../../wasp/client/operations';
import { AXIE_STUDIO_BRAND } from '../config/brand';
import toast from 'react-hot-toast';

export function PricingPage() {
  const { data: user } = useAuth();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const handleSubscribe = async (planId: string, planName: string) => {
    if (!user) {
      window.location.href = '/signup';
      return;
    }

    setIsLoading(planId);
    try {
      const session = await createCheckoutSession({
        priceId: planId,
        customerId: user.stripeId,
      });
      
      if (session.sessionUrl) {
        window.location.href = session.sessionUrl;
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Failed to create checkout session. Please try again.');
    } finally {
      setIsLoading(null);
    }
  };

  const plans = [
    {
      name: "Starter",
      description: "Perfect for growing teams ready to automate with enterprise-grade AI",
      price: billingCycle === 'monthly' ? 49 : 490,
      originalPrice: billingCycle === 'yearly' ? 588 : null,
      features: AXIE_STUDIO_BRAND.features.starter.features,
      cta: "Start Enterprise Trial",
      popular: false,
      priceId: process.env.REACT_APP_STRIPE_STARTER_PRICE_ID,
      badge: "Most Popular for SMB"
    },
    {
      name: "Professional",
      description: "For enterprises scaling AI automation across departments",
      price: billingCycle === 'monthly' ? 149 : 1490,
      originalPrice: billingCycle === 'yearly' ? 1788 : null,
      features: AXIE_STUDIO_BRAND.features.pro.features,
      cta: "Start Enterprise Trial",
      popular: true,
      priceId: process.env.REACT_APP_STRIPE_PRO_PRICE_ID,
      badge: "Best Value"
    },
    {
      name: "Enterprise",
      description: "For Fortune 500 companies requiring maximum security and customization",
      price: "Custom",
      features: AXIE_STUDIO_BRAND.features.enterprise.features,
      cta: "Contact Sales",
      popular: false,
      priceId: "enterprise",
      badge: "White Glove Service"
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-gradient-to-br from-slate-50 to-blue-50 border-b border-slate-200">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="mb-6">
              <span className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 border border-indigo-200">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                Trusted by Fortune 500 Companies • 30-Day Free Trial
              </span>
            </div>
            <h1 className="text-5xl lg:text-6xl font-bold text-slate-900 mb-6 leading-tight">
              Enterprise AI Automation
              <span className="block bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
                Pricing Plans
              </span>
            </h1>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Choose the perfect plan for your organization. Scale AI automation across your enterprise with transparent, predictable pricing.
            </p>
          </div>
          
          {/* Billing Toggle */}
          <div className="flex justify-center mt-12">
            <div className="bg-white p-2 rounded-2xl shadow-lg border border-slate-200">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-8 py-3 rounded-xl font-semibold transition-all ${
                  billingCycle === 'monthly'
                    ? 'bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                Monthly Billing
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={`px-8 py-3 rounded-xl font-semibold transition-all ${
                  billingCycle === 'yearly'
                    ? 'bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                Annual Billing
                <span className="ml-2 text-xs bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full font-bold">
                  Save 17%
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {plans.map((plan, index) => (
            <div
              key={plan.name}
              className={`bg-white rounded-2xl shadow-lg overflow-hidden relative ${
                plan.popular ? 'ring-2 ring-purple-500 scale-105' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <span className="bg-gradient-to-r from-purple-500 to-cyan-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                    🔥 Most Popular
                  </span>
                </div>
              )}
              
              <div className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-6">{plan.description}</p>
                  
                  <div className="mb-6">
                    {typeof plan.price === 'number' ? (
                      <div>
                        <div className="flex items-center justify-center">
                          <span className="text-5xl font-bold text-gray-900">${plan.price}</span>
                          <span className="text-gray-600 ml-2">
                            /{billingCycle === 'monthly' ? 'month' : 'year'}
                          </span>
                        </div>
                        {plan.originalPrice && (
                          <div className="text-sm text-gray-500 mt-1">
                            <span className="line-through">${plan.originalPrice}/year</span>
                            <span className="text-green-600 ml-2 font-medium">
                              Save ${plan.originalPrice - plan.price}
                            </span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-4xl font-bold text-gray-900">{plan.price}</div>
                    )}
                  </div>
                </div>
                
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-purple-500 mr-3 mt-1">✓</span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <button
                  onClick={() => plan.priceId !== 'enterprise' 
                    ? handleSubscribe(plan.priceId!, plan.name.toLowerCase())
                    : window.open('mailto:<EMAIL>', '_blank')
                  }
                  disabled={isLoading === plan.name.toLowerCase()}
                  className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-900 hover:bg-gray-800 text-white'
                  } ${isLoading === plan.name.toLowerCase() ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isLoading === plan.name.toLowerCase() ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing...
                    </div>
                  ) : (
                    plan.cta
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                q: "What counts as a workflow execution?",
                a: "Each time your workflow runs from start to finish counts as one execution. This includes manual triggers, scheduled runs, and API calls."
              },
              {
                q: "Can I change plans anytime?",
                a: "Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences."
              },
              {
                q: "What integrations are available?",
                a: "Axie Studio connects to 300+ services including OpenAI, Anthropic, Google, Slack, Discord, Zapier, and many more. Custom integrations available on Enterprise."
              },
              {
                q: "Is there a free trial?",
                a: "Yes! All paid plans come with a 14-day free trial. No credit card required to start building."
              },
              {
                q: "What kind of support do you offer?",
                a: "Starter plans get email support, Pro plans get priority support, and Enterprise gets a dedicated support manager with custom SLA."
              },
              {
                q: "Can I export my workflows?",
                a: "Yes! You can export your workflows as JSON files and import them into other Axie Studio accounts or self-hosted instances."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="font-semibold text-gray-900 mb-2">{faq.q}</h3>
                <p className="text-gray-600">{faq.a}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
