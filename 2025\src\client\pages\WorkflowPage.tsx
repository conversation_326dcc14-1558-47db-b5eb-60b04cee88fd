import React, { useEffect, useState } from 'react';
import { useAuth } from 'wasp/client/auth';
import { Link, useSearchParams } from 'react-router-dom';

export function WorkflowPage({ user }: { user: any }) {
  const [searchParams] = useSearchParams();
  const [axieStudioUrl, setAxieStudioUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const workflowId = searchParams.get('id');

  useEffect(() => {
    const generateAxieStudioAccess = async () => {
      try {
        const response = await fetch('/api/axie/access-token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            userId: user.id,
            workflowId: workflowId 
          })
        });
        
        const { accessUrl } = await response.json();
        setAxieStudioUrl(accessUrl);
      } catch (error) {
        console.error('Failed to generate Axie Studio access:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user.subscriptionStatus === 'active') {
      generateAxieStudioAccess();
    } else {
      setIsLoading(false);
    }
  }, [user, workflowId]);

  if (user.subscriptionStatus !== 'active') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-cyan-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-3xl">🔒</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Subscription Required</h1>
          <p className="text-gray-600 mb-6">
            Upgrade to access Axie Studio's powerful visual workflow builder and start creating AI automations.
          </p>
          <Link
            to="/pricing"
            className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-lg font-medium"
          >
            View Plans
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link 
            to="/dashboard" 
            className="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors"
          >
            <span>←</span>
            <span>Dashboard</span>
          </Link>
          
          <div className="h-6 w-px bg-gray-300"></div>
          
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">A</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">Axie Studio</h1>
              <p className="text-xs text-gray-500">Visual AI Workflow Builder</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">
              {user.subscriptionPlan?.charAt(0).toUpperCase() + user.subscriptionPlan?.slice(1) || 'Free'} Plan
            </div>
            <div className="text-xs text-gray-500">
              {user.creditsTotal - user.creditsUsed} credits remaining
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
              <span>💾</span>
            </button>
            <button className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
              <span>▶️</span>
            </button>
            <button className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
              <span>⚙️</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Workflow Area */}
      <div className="flex-1 relative">
        {isLoading ? (
          <div className="flex items-center justify-center h-full bg-gray-50">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <span className="text-white font-bold text-2xl">A</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Axie Studio...</h3>
              <p className="text-gray-600">Preparing your visual workflow builder</p>
              <div className="mt-4 flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
            </div>
          </div>
        ) : axieStudioUrl ? (
          <iframe
            src={axieStudioUrl}
            className="w-full h-full border-0"
            title="Axie Studio Visual Workflow Builder"
            allow="camera; microphone; clipboard-read; clipboard-write"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-50">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚠️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Load Axie Studio</h3>
              <p className="text-gray-600 mb-4">
                We're having trouble connecting to the workflow builder. Please try again.
              </p>
              <button 
                onClick={() => window.location.reload()}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium"
              >
                Retry
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 text-white px-6 py-2 text-xs flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <span className="flex items-center space-x-1">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>Connected</span>
          </span>
          <span>Axie Studio v2.1.0</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <span>Auto-save: On</span>
          <span>Last saved: Just now</span>
        </div>
      </div>
    </div>
  );
}
