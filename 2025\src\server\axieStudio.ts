import type { AxieStudioProxy } from 'wasp/server/api';
import jwt from 'jsonwebtoken';
import { prisma } from 'wasp/server';

export const axieStudioProxy: AxieStudioProxy = async (req, res, context) => {
  if (!context.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check subscription status
  if (context.user.subscriptionStatus !== 'active') {
    return res.status(403).json({ 
      error: 'Active subscription required',
      upgradeUrl: '/pricing'
    });
  }

  try {
    const langflowBaseUrl = process.env.LANGFLOW_BASE_URL;
    const targetPath = req.url?.replace('/api/axie', '') || '';
    
    // Handle different endpoints
    switch (true) {
      case targetPath === '/access-token':
        return handleAccessToken(req, res, context.user);
      
      case targetPath.startsWith('/workflows'):
        return handleWorkflowOperations(req, res, context.user, targetPath);
      
      case targetPath.startsWith('/execute'):
        return handleWorkflowExecution(req, res, context.user, targetPath);
      
      default:
        return proxyToLangflow(req, res, context.user, targetPath);
    }
  } catch (error) {
    console.error('Axie Studio proxy error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

async function handleAccessToken(req: any, res: any, user: any) {
  const accessToken = jwt.sign(
    { 
      userId: user.id,
      email: user.email,
      plan: user.subscriptionPlan,
      credits: user.creditsTotal - user.creditsUsed,
    },
    process.env.JWT_SECRET!,
    { expiresIn: '2h' }
  );
  
  const accessUrl = `${process.env.LANGFLOW_BASE_URL}?token=${accessToken}&user=${user.id}&plan=${user.subscriptionPlan}`;
  return res.json({ 
    accessUrl,
    expiresIn: 7200, // 2 hours
    userLimits: {
      maxWorkflows: getMaxWorkflows(user.subscriptionPlan),
      creditsRemaining: user.creditsTotal - user.creditsUsed,
    }
  });
}

async function handleWorkflowOperations(req: any, res: any, user: any, targetPath: string) {
  // Proxy workflow operations to Langflow
  const response = await fetch(`${process.env.LANGFLOW_BASE_URL}${targetPath}`, {
    method: req.method,
    headers: {
      ...req.headers,
      'x-api-key': user.axieStudioApiKey!,
      'x-user-id': user.id,
      'x-user-plan': user.subscriptionPlan || 'free',
    },
    body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
  });

  const data = await response.json();
  res.status(response.status).json(data);
}

async function handleWorkflowExecution(req: any, res: any, user: any, targetPath: string) {
  // Check if user has enough credits
  if (user.creditsUsed >= user.creditsTotal) {
    return res.status(402).json({ 
      error: 'Insufficient credits',
      creditsUsed: user.creditsUsed,
      creditsTotal: user.creditsTotal,
      upgradeUrl: '/pricing'
    });
  }

  // Proxy the execution request
  const response = await fetch(`${process.env.LANGFLOW_BASE_URL}${targetPath}`, {
    method: req.method,
    headers: {
      ...req.headers,
      'x-api-key': user.axieStudioApiKey!,
      'x-user-id': user.id,
      'x-user-plan': user.subscriptionPlan || 'free',
    },
    body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
  });

  if (response.ok) {
    // Increment credit usage
    await prisma.user.update({
      where: { id: user.id },
      data: { creditsUsed: { increment: 1 } }
    });
  }

  const data = await response.json();
  res.status(response.status).json(data);
}

async function proxyToLangflow(req: any, res: any, user: any, targetPath: string) {
  // Generic proxy for other requests
  const response = await fetch(`${process.env.LANGFLOW_BASE_URL}${targetPath}`, {
    method: req.method,
    headers: {
      ...req.headers,
      'x-api-key': user.axieStudioApiKey!,
      'x-user-id': user.id,
      'x-user-plan': user.subscriptionPlan || 'free',
    },
    body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
  });

  const data = await response.json();
  res.status(response.status).json(data);
}

function getMaxWorkflows(plan: string | null): number {
  switch (plan) {
    case 'starter': return 10;
    case 'pro': return 100;
    case 'enterprise': return 999;
    default: return 0;
  }
}
