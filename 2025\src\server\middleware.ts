import type { MiddlewareConfigFn } from "wasp/server";
import cors from 'cors';

export const serverMiddlewareFn: MiddlewareConfigFn = (middlewareConfig) => {
  // Enable CORS for API endpoints
  middlewareConfig.set('cors', cors({
    origin: [
      'http://localhost:3000',
      'https://vercel-frontend-three-rho.vercel.app',
      process.env.CLIENT_URL || 'http://localhost:3000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key', 'x-user-id', 'x-user-plan'],
  }));

  return middlewareConfig;
};

export const corsMiddleware: MiddlewareConfigFn = (config) => {
  // Enable CORS for Axie Studio API
  config.set('cors', {
    origin: [
      'http://localhost:3000',
      'https://vercel-frontend-three-rho.vercel.app',
      'https://langflow-tv34o.ondigitalocean.app',
      process.env.CLIENT_URL || 'http://localhost:3000',
      process.env.LANGFLOW_BASE_URL || 'https://langflow-tv34o.ondigitalocean.app'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key', 'x-user-id', 'x-user-plan'],
  });
  return config;
};
