import Stripe from 'stripe';
import { prisma } from 'wasp/server';
import type { CreateCheckoutSession } from 'wasp/server/operations';

const stripe = new Stripe(process.env.STRIPE_API_KEY!, {
  apiVersion: '2023-10-16',
});

export const createCheckoutSession: CreateCheckoutSession = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  try {
    // Create or get Stripe customer
    let customerId = context.user.stripeId;
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: context.user.email!,
        metadata: {
          userId: context.user.id,
        },
      });
      
      customerId = customer.id;
      
      // Update user with Stripe customer ID
      await prisma.user.update({
        where: { id: context.user.id },
        data: { stripeId: customerId },
      });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: args.priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.CLIENT_URL}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.CLIENT_URL}/pricing`,
      metadata: {
        userId: context.user.id,
      },
    });

    // Update user with checkout session ID
    await prisma.user.update({
      where: { id: context.user.id },
      data: { checkoutSessionId: session.id },
    });

    return {
      sessionId: session.id,
      sessionUrl: session.url,
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new Error('Failed to create checkout session');
  }
};
