import { prisma } from 'wasp/server';
import type { GetUserStats, UpdateUserProfile } from 'wasp/server/operations';

export const getUserStats: GetUserStats = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  const user = await prisma.user.findUnique({
    where: { id: context.user.id },
    include: {
      workflows: {
        select: {
          id: true,
          name: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      },
    },
  });

  if (!user) {
    throw new Error('User not found');
  }

  // Get usage stats from Axie Studio
  const usageStats = await getAxieStudioUsage(user.id);

  return {
    user: {
      ...user,
      hashedPassword: undefined, // Remove sensitive data
    },
    stats: {
      totalWorkflows: user.workflows.length,
      activeWorkflows: user.workflows.filter(w => w.isActive).length,
      creditsRemaining: user.creditsTotal - user.creditsUsed,
      ...usageStats,
    },
  };
};

export const updateUserProfile: UpdateUserProfile = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  return await prisma.user.update({
    where: { id: context.user.id },
    data: {
      username: args.username,
      // Add other updatable fields as needed
    },
  });
};

async function getAxieStudioUsage(userId: string) {
  try {
    const response = await fetch(`${process.env.LANGFLOW_BASE_URL}/api/v1/users/${userId}/stats`, {
      headers: {
        'x-api-key': process.env.LANGFLOW_API_KEY!,
      },
    });
    
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.error('Failed to get Axie Studio usage:', error);
  }
  
  return {
    executionsThisMonth: 0,
    lastExecutionAt: null,
    popularWorkflows: [],
  };
}
