import { prisma } from 'wasp/server';
import type { CreateWorkflow, GetUserWorkflows, DeleteWorkflow } from 'wasp/server/operations';

export const getUserWorkflows: GetUserWorkflows = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  return await prisma.workflow.findMany({
    where: { userId: context.user.id },
    orderBy: { updatedAt: 'desc' },
  });
};

export const createWorkflow: CreateWorkflow = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  // Check subscription limits
  const user = await prisma.user.findUnique({
    where: { id: context.user.id },
    include: { workflows: true },
  });

  if (!user || user.subscriptionStatus !== 'active') {
    throw new Error('Active subscription required');
  }

  const maxWorkflows = getMaxWorkflows(user.subscriptionPlan);
  if (user.workflows.length >= maxWorkflows) {
    throw new Error(`Workflow limit reached. Upgrade your plan to create more workflows.`);
  }

  // Create workflow in local database
  const workflow = await prisma.workflow.create({
    data: {
      name: args.name,
      description: args.description,
      userId: context.user.id,
    },
  });

  // Create workflow in Axie Studio
  try {
    const langflowResponse = await fetch(`${process.env.LANGFLOW_BASE_URL}/api/v1/flows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': user.axieStudioApiKey!,
        'x-user-id': user.id,
      },
      body: JSON.stringify({
        name: args.name,
        description: args.description,
        data: args.flowData || {},
      }),
    });

    if (langflowResponse.ok) {
      const langflowData = await langflowResponse.json();
      
      // Update workflow with Langflow ID
      await prisma.workflow.update({
        where: { id: workflow.id },
        data: { langflowId: langflowData.id },
      });
    }
  } catch (error) {
    console.error('Failed to create workflow in Axie Studio:', error);
    // Don't fail the entire operation, just log the error
  }

  return workflow;
};

export const deleteWorkflow: DeleteWorkflow = async (args, context) => {
  if (!context.user) {
    throw new Error('User not authenticated');
  }

  const workflow = await prisma.workflow.findFirst({
    where: {
      id: args.workflowId,
      userId: context.user.id,
    },
  });

  if (!workflow) {
    throw new Error('Workflow not found');
  }

  // Delete from Axie Studio if it exists there
  if (workflow.langflowId) {
    try {
      await fetch(`${process.env.LANGFLOW_BASE_URL}/api/v1/flows/${workflow.langflowId}`, {
        method: 'DELETE',
        headers: {
          'x-api-key': context.user.axieStudioApiKey!,
          'x-user-id': context.user.id,
        },
      });
    } catch (error) {
      console.error('Failed to delete workflow from Axie Studio:', error);
    }
  }

  // Delete from local database
  await prisma.workflow.delete({
    where: { id: args.workflowId },
  });

  return { success: true };
};

function getMaxWorkflows(plan: string | null): number {
  switch (plan) {
    case 'starter': return 10;
    case 'pro': return 100;
    case 'enterprise': return 999;
    default: return 0;
  }
}
