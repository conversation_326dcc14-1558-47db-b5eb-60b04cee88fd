import type { PaymentsWebhook } from 'wasp/server/api';
import Stripe from 'stripe';
import { prisma } from 'wasp/server';

const stripe = new Stripe(process.env.STRIPE_API_KEY!, {
  apiVersion: '2023-10-16',
});

export const paymentsWebhook: PaymentsWebhook = async (req, res, context) => {
  const sig = req.headers['stripe-signature'] as string;
  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return res.status(400).send('Webhook signature verification failed');
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object as Stripe.Subscription);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
};

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const customerId = session.customer as string;
  const subscriptionId = session.subscription as string;
  
  // Get subscription details
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  const priceId = subscription.items.data[0].price.id;
  
  // Determine plan based on price ID
  const planMapping: Record<string, { plan: string; credits: number }> = {
    [process.env.STRIPE_STARTER_PRICE_ID!]: { plan: 'starter', credits: 2500 },
    [process.env.STRIPE_PRO_PRICE_ID!]: { plan: 'pro', credits: 25000 },
    [process.env.STRIPE_ENTERPRISE_PRICE_ID!]: { plan: 'enterprise', credits: 100000 },
  };
  
  const planInfo = planMapping[priceId] || { plan: 'starter', credits: 2500 };
  
  // Update user in database
  await prisma.user.update({
    where: { stripeId: customerId },
    data: {
      subscriptionStatus: 'active',
      subscriptionPlan: planInfo.plan,
      subscriptionId: subscriptionId,
      creditsTotal: planInfo.credits,
      creditsUsed: 0,
    },
  });
  
  // Generate Axie Studio API key for user
  await generateAxieStudioApiKey(customerId, planInfo.plan);
  
  // Send welcome email
  await sendWelcomeEmail(customerId, planInfo.plan);
}

async function handleSubscriptionChange(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;
  const priceId = subscription.items.data[0].price.id;
  
  const planMapping: Record<string, { plan: string; credits: number }> = {
    [process.env.STRIPE_STARTER_PRICE_ID!]: { plan: 'starter', credits: 2500 },
    [process.env.STRIPE_PRO_PRICE_ID!]: { plan: 'pro', credits: 25000 },
    [process.env.STRIPE_ENTERPRISE_PRICE_ID!]: { plan: 'enterprise', credits: 100000 },
  };
  
  const planInfo = planMapping[priceId] || { plan: 'starter', credits: 2500 };
  
  await prisma.user.update({
    where: { stripeId: customerId },
    data: {
      subscriptionStatus: subscription.status,
      subscriptionPlan: planInfo.plan,
      subscriptionId: subscription.id,
      creditsTotal: planInfo.credits,
    },
  });
}

async function handleSubscriptionCanceled(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;
  
  await prisma.user.update({
    where: { stripeId: customerId },
    data: {
      subscriptionStatus: 'canceled',
      subscriptionId: null,
    },
  });
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  // Handle successful payment - could reset credits, send receipt, etc.
  console.log('Payment succeeded for invoice:', invoice.id);
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  // Handle failed payment - could send notification, update status, etc.
  console.log('Payment failed for invoice:', invoice.id);
}

async function generateAxieStudioApiKey(stripeId: string, plan: string) {
  const user = await prisma.user.findUnique({
    where: { stripeId }
  });
  
  if (!user) return;
  
  // Generate secure API key for Axie Studio
  const apiKey = `axie_${user.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  await prisma.user.update({
    where: { id: user.id },
    data: { axieStudioApiKey: apiKey }
  });
  
  // Register user in Axie Studio backend
  await registerUserInAxieStudio(user, apiKey, plan);
}

async function registerUserInAxieStudio(user: any, apiKey: string, plan: string) {
  try {
    await fetch(`${process.env.LANGFLOW_BASE_URL}/api/v1/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.LANGFLOW_API_KEY!,
      },
      body: JSON.stringify({
        userId: user.id,
        email: user.email,
        apiKey: apiKey,
        plan: plan,
        maxWorkflows: plan === 'starter' ? 10 : plan === 'pro' ? 100 : 999,
        maxExecutions: plan === 'starter' ? 2500 : plan === 'pro' ? 25000 : 999999,
      }),
    });
  } catch (error) {
    console.error('Failed to register user in Axie Studio:', error);
  }
}

async function sendWelcomeEmail(stripeId: string, plan: string) {
  // TODO: Implement email sending
  console.log(`Sending welcome email for plan: ${plan}`);
}
