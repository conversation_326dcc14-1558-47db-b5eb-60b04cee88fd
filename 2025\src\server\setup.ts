import { prisma } from 'wasp/server';

export default async function setup() {
  console.log('🚀 Setting up Axie Studio server...');
  
  try {
    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    
    // Test Langflow connection
    if (process.env.LANGFLOW_BASE_URL && process.env.LANGFLOW_API_KEY) {
      try {
        const response = await fetch(`${process.env.LANGFLOW_BASE_URL}/api/v1/health`, {
          headers: {
            'x-api-key': process.env.LANGFLOW_API_KEY,
          },
        });
        
        if (response.ok) {
          console.log('✅ Axie Studio (Langflow) connection successful');
        } else {
          console.warn('⚠️ Axie Studio (Langflow) connection failed');
        }
      } catch (error) {
        console.warn('⚠️ Could not connect to Axie Studio (Langflow):', error);
      }
    } else {
      console.warn('⚠️ Axie Studio (Langflow) environment variables not configured');
    }
    
    // Verify Stripe configuration
    if (process.env.STRIPE_API_KEY && process.env.STRIPE_WEBHOOK_SECRET) {
      console.log('✅ Stripe configuration found');
    } else {
      console.warn('⚠️ Stripe configuration incomplete');
    }
    
    console.log('🎉 Axie Studio server setup complete!');
  } catch (error) {
    console.error('❌ Server setup failed:', error);
    throw error;
  }
}
